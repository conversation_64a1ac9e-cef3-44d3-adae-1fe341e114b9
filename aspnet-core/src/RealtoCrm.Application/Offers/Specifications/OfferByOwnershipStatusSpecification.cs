namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq.Expressions;
using Common.Models;
using Expressions;

public class OfferByOwnershipStatusSpecification(OwnershipStatus? ownershipStatus, long? userId) : Specification<Offer>
{
    protected override bool Include => ownershipStatus is not null && userId != null;

    public override Expression<Func<Offer, bool>> ToExpression()
        => ownershipStatus switch
        {
            OwnershipStatus.All => IsActiveOrPotentialOffer(),
            OwnershipStatus.Mine => IsMineAndCorrectOffer(userId),
            _ => offer => false
        };

    private static Expression<Func<Offer, bool>> IsActiveOrPotentialOffer()
        => offer => offer.OfferStatusId == (int)OfferStatuses.Potential ||
                    offer.OfferStatusId == (int)OfferStatuses.Active;

    private static Expression<Func<Offer, bool>> IsMineOffer(long? userId)
        => offer => userId != null && offer.Employee!.UserAccount.UserId == userId;

    private static Expression<Func<Offer, bool>> IsDraftOffer()
        => offer => offer.OfferStatusId == (int)OfferStatuses.Draft;

    private static Expression<Func<Offer, bool>> IsMineAndCorrectOffer(long? userId)
        => ExpressionsHelper.And(IsMineOffer(userId),
            ExpressionsHelper.Or(IsActiveOrPotentialOffer(),
                IsDraftOffer()));
}