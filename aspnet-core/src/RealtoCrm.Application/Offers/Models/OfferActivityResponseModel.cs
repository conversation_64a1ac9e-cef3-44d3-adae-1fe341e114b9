using RealtoCrm.Deposits.Models;

namespace RealtoCrm.Offers.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Comments.Models;
using Deals.Models;
using Mapping;
using Tasks.Models;
using Viewings;
using Viewings.Models;

public class OfferActivityResponseModel : IMapFrom<Offer>, IMapExplicitly
{
    public DateTime ShowFromDate => DateTime.Now.AddMonths(-2);
    
    public ICollection<TaskActivityResponseModel> OffersTasks { get; init; } = new List<TaskActivityResponseModel>();
    
    public ICollection<CommentResponseModel> Comments { get; init; } = new List<CommentResponseModel>();
    
    public List<ViewingResponseModel> Viewings { get; } = new ();
    
    public List<DealShortResponseModel> Deals { get; } = new ();
    
    public List<DepositListingResponseModel> Deposits { get; init; } = new ();

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Offer, OfferActivityResponseModel>()
            .ForMember(m => m.Comments, cfg => cfg
                .MapFrom(m => m.OffersComments.Select(oc => oc.Comment).OrderByDescending(oc => oc.CreationTime)))
            .ForMember(m => m.OffersTasks, cfg => cfg
                .MapFrom(m => m.OffersTasks.OrderByDescending(ot => ot.CreationTime)))
            .ForMember(m => m.Deposits, cfg => cfg
                .MapFrom(m => m.Deposits.OrderByDescending(ot => ot.CreationTime)));
}