using System;
using System.Linq.Expressions;

namespace RealtoCrm.Deposits.Specifications;

public class DepositByConsultantSpecification(string? consultant) : Specification<Deposit>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(consultant);

    public override Expression<Func<Deposit, bool>> ToExpression() => deposit
        => deposit.SearchEmployee.FullName.Contains(consultant!) ||
           deposit.OfferEmployee.FullName.Contains(consultant!);
}