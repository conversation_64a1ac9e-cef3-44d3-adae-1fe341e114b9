using System.ComponentModel.DataAnnotations.Schema;

namespace RealtoCrm.Employees;

using System.Collections.Generic;
using Abp.Authorization.Users;
using Abp.Domain.Entities.Auditing;
using Clients;
using Comments;
using Companies;
using CompanyPositions;
using Deals;
using Meetings;
using Offers;
using Projects;
using Searches;
using SourceCategories;
using Tasks;
using Viewings;

public class Employee : FullAuditedEntity<int>
{
    public string? FirstName { get; set; }

    public string? MiddleName { get; set; }

    public string? LastName { get; set; }

    public string? DisplayName { get; set; }

    public string? PhoneNumber { get; set; }

    public string? SimCardNumber { get; set; }

    public string? WorkPosition { get; set; }

    public string? IdentificationNumber { get; set; }

    public long UserAccountId { get; set; }

    public UserAccount UserAccount { get; set; } = default!;

    public int? OfficeId { get; set; }

    public Office? Office { get; set; }

    public int? TeamId { get; set; }

    public Team? Team { get; set; }

    public EmployeeMapping? EmployeeMapping { get; set; }

    public int? DepartmentId { get; set; }

    public Department? Department { get; set; }

    public int? DivisionId { get; set; }

    public Division? Division { get; set; }

    public int? CompanyId { get; set; }

    public Company? Company { get; set; }

    public int? CompanyPositionId { get; set; }

    public CompanyPosition? CompanyPosition { get; set; }

    public int? ManagerId { get; set; }

    public Employee? Manager { get; set; }

    public int? AssistsToId { get; set; }

    public Employee? AssistsTo { get; set; }

    public ICollection<Employee> Assistants { get; } = new List<Employee>();

    public ICollection<Employee> Employees { get; } = new List<Employee>();

    public ICollection<Offer> Offers { get; } = new List<Offer>();

    public ICollection<Search> Searches { get; } = new List<Search>();

    public ICollection<Task> Tasks { get; } = new List<Task>();

    public ICollection<Comment> Comments { get; } = new List<Comment>();

    public ICollection<Viewing> SearchViewings { get; } = new List<Viewing>();

    public ICollection<Viewing> OfferViewings { get; } = new List<Viewing>();

    public ICollection<ProjectEmployee> ProjectsEmployees { get; } = new List<ProjectEmployee>();

    public ICollection<ClientSourceCategory> ClientsSourceCategories { get; } = new List<ClientSourceCategory>();

    public ICollection<ProjectSourceCategory> ProjectsSourceCategories { get; } = new List<ProjectSourceCategory>();

    public ICollection<Meeting> Meetings { get; } = new List<Meeting>();

    public ICollection<SourceDetail> Details { get; } = new List<SourceDetail>();

    public ICollection<Deal> Deals { get; } = new List<Deal>();

    public ICollection<Deal> OppositeSideDeals { get; } = new List<Deal>();

    public ICollection<DealParticipant> DealParticipants { get; } = new List<DealParticipant>();

    public ICollection<DealParticipation> DealParticipations { get; } = new List<DealParticipation>();
}